// TechTRUV Main JavaScript File

$(document).ready(function() {
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 70
            }, 1000);
        }
    });

    // Navbar background change on scroll
    $(window).scroll(function() {
        if ($(window).scrollTop() > 50) {
            $('.navbar').addClass('navbar-scrolled');
        } else {
            $('.navbar').removeClass('navbar-scrolled');
        }
    });

    // Contact form submission
    $('#contactForm').on('submit', function(e) {
        e.preventDefault();
        
        // Get form data
        var formData = {
            name: $('#name').val(),
            email: $('#email').val(),
            subject: $('#subject').val(),
            message: $('#message').val(),
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        // Basic validation
        if (!formData.name || !formData.email || !formData.message) {
            showAlert('Please fill in all required fields.', 'danger');
            return;
        }

        // Email validation
        if (!isValidEmail(formData.email)) {
            showAlert('Please enter a valid email address.', 'danger');
            return;
        }

        // Submit form via AJAX
        $.ajax({
            url: '/contact',
            type: 'POST',
            data: formData,
            beforeSend: function() {
                $('#submitBtn').prop('disabled', true).text('Sending...');
            },
            success: function(response) {
                showAlert('Thank you! Your message has been sent successfully.', 'success');
                $('#contactForm')[0].reset();
            },
            error: function(xhr) {
                var errorMessage = 'An error occurred. Please try again.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                showAlert(errorMessage, 'danger');
            },
            complete: function() {
                $('#submitBtn').prop('disabled', false).text('Send Message');
            }
        });
    });

    // Newsletter subscription
    $('#newsletterForm').on('submit', function(e) {
        e.preventDefault();
        
        var email = $('#newsletterEmail').val();
        
        if (!email || !isValidEmail(email)) {
            showAlert('Please enter a valid email address.', 'danger');
            return;
        }

        $.ajax({
            url: '/newsletter/subscribe',
            type: 'POST',
            data: {
                email: email,
                _token: $('meta[name="csrf-token"]').attr('content')
            },
            beforeSend: function() {
                $('#newsletterBtn').prop('disabled', true).text('Subscribing...');
            },
            success: function(response) {
                showAlert('Thank you for subscribing to our newsletter!', 'success');
                $('#newsletterForm')[0].reset();
            },
            error: function(xhr) {
                showAlert('An error occurred. Please try again.', 'danger');
            },
            complete: function() {
                $('#newsletterBtn').prop('disabled', false).text('Subscribe');
            }
        });
    });

    // Portfolio filter functionality
    $('.portfolio-filter').on('click', function() {
        var filter = $(this).data('filter');
        
        $('.portfolio-filter').removeClass('active');
        $(this).addClass('active');
        
        if (filter === 'all') {
            $('.portfolio-item').fadeIn();
        } else {
            $('.portfolio-item').hide();
            $('.portfolio-item[data-category="' + filter + '"]').fadeIn();
        }
    });

    // Testimonial carousel (if using)
    if ($('.testimonial-carousel').length) {
        $('.testimonial-carousel').slick({
            dots: true,
            infinite: true,
            speed: 300,
            slidesToShow: 1,
            adaptiveHeight: true,
            autoplay: true,
            autoplaySpeed: 5000
        });
    }

    // Counter animation
    function animateCounters() {
        $('.counter').each(function() {
            var $this = $(this);
            var countTo = $this.attr('data-count');
            
            $({ countNum: $this.text() }).animate({
                countNum: countTo
            }, {
                duration: 2000,
                easing: 'linear',
                step: function() {
                    $this.text(Math.floor(this.countNum));
                },
                complete: function() {
                    $this.text(this.countNum);
                }
            });
        });
    }

    // Trigger counter animation when in viewport
    $(window).scroll(function() {
        $('.counter').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                if (!$(this).hasClass('animated')) {
                    $(this).addClass('animated');
                    animateCounters();
                }
            }
        });
    });

    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('#backToTop').fadeIn();
        } else {
            $('#backToTop').fadeOut();
        }
    });

    $('#backToTop').click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
        return false;
    });

    // Mobile menu toggle
    $('.navbar-toggler').click(function() {
        $(this).toggleClass('active');
    });

    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').click(function() {
        if ($(window).width() < 992) {
            $('.navbar-collapse').collapse('hide');
            $('.navbar-toggler').removeClass('active');
        }
    });

    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Pricing toggle (if you have monthly/yearly pricing)
    $('.pricing-toggle').change(function() {
        if ($(this).is(':checked')) {
            $('.monthly-price').hide();
            $('.yearly-price').show();
        } else {
            $('.yearly-price').hide();
            $('.monthly-price').show();
        }
    });

    // FAQ accordion
    $('.faq-question').click(function() {
        $(this).next('.faq-answer').slideToggle();
        $(this).find('.faq-icon').toggleClass('rotated');
    });

    // Service tabs
    $('.service-tab').click(function() {
        var target = $(this).data('target');
        
        $('.service-tab').removeClass('active');
        $(this).addClass('active');
        
        $('.service-content').removeClass('active');
        $(target).addClass('active');
    });

});

// Utility Functions
function isValidEmail(email) {
    var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showAlert(message, type) {
    var alertClass = 'alert-' + type;
    var alertHtml = '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
                    message +
                    '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
                    '</div>';
    
    $('#alertContainer').html(alertHtml);
    
    // Auto-hide success alerts after 5 seconds
    if (type === 'success') {
        setTimeout(function() {
            $('.alert').alert('close');
        }, 5000);
    }
}

// Loading animation
function showLoading() {
    $('#loadingOverlay').show();
}

function hideLoading() {
    $('#loadingOverlay').hide();
}

// Format numbers with commas
function numberWithCommas(x) {
    return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

// Debounce function for search
function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}
