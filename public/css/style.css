/* TechTRUV Custom Styles */

/* Root Variables */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --info-color: #17a2b8;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --white: #ffffff;
    --black: #000000;
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--dark-color);
    background-color: var(--white);
}

/* Header Styles */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.navbar-nav .nav-link {
    font-weight: 500;
    margin: 0 10px;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: var(--primary-color) !important;
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    color: var(--white);
    padding: 100px 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: bold;
    margin-bottom: 20px;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

/* Section Styles */
.section-padding {
    padding: 80px 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: bold;
    text-align: center;
    margin-bottom: 20px;
    color: var(--dark-color);
}

.section-subtitle {
    font-size: 1.1rem;
    text-align: center;
    margin-bottom: 50px;
    color: var(--secondary-color);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* Card Styles */
.custom-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
}

.custom-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.card-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 20px;
}

/* Button Styles */
.btn-primary-custom {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 12px 30px;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-primary-custom:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-2px);
}

.btn-outline-primary-custom {
    color: var(--primary-color);
    border-color: var(--primary-color);
    padding: 12px 30px;
    font-weight: 500;
    border-radius: 25px;
    transition: all 0.3s ease;
}

.btn-outline-primary-custom:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-2px);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 50px 0 20px;
}

.footer h5 {
    color: var(--white);
    margin-bottom: 20px;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid #495057;
    padding-top: 20px;
    margin-top: 30px;
    text-align: center;
    color: #adb5bd;
}

/* Testimonial Styles */
.testimonial-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 30px;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 20px;
    object-fit: cover;
}

.testimonial-stars {
    color: #ffc107;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

/* Contact Form Styles */
.contact-form {
    background: var(--light-color);
    padding: 40px;
    border-radius: 10px;
}

.form-control {
    border-radius: 5px;
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Pricing Card Styles */
.pricing-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.pricing-price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 20px 0;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.pricing-features li {
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.pricing-features li:last-child {
    border-bottom: none;
}

/* Blog Card Styles */
.blog-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card img {
    height: 200px;
    object-fit: cover;
}

.blog-meta {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

/* Portfolio Styles */
.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 30px;
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .section-padding {
        padding: 50px 0;
    }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-light-custom {
    background-color: #f8f9fa !important;
}

.mt-100 {
    margin-top: 100px;
}

.mb-100 {
    margin-bottom: 100px;
}
