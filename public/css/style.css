/* TechTRUV Custom Styles */

/* Root Variables - Professional IT Style */
:root {
    --primary-color: #1e3a8a;        /* Deep Navy Blue */
    --secondary-color: #64748b;      /* Slate Gray */
    --accent-color: #06b6d4;         /* <PERSON>an/Teal */
    --success-color: #10b981;        /* Emerald Green */
    --info-color: #3b82f6;           /* Blue */
    --warning-color: #f59e0b;        /* Amber */
    --danger-color: #ef4444;         /* Red */
    --light-color: #f8fafc;          /* Very Light Gray */
    --dark-color: #0f172a;           /* Very Dark Navy */
    --white: #ffffff;                /* Pure White */
    --black: #000000;                /* Pure Black */
    --text-light: #ffffff;           /* White Text */
    --text-dark: #1e293b;            /* Dark Slate Text */
    --text-muted: #64748b;           /* Muted Gray Text */
    --font-family: 'Poppins', 'Montserrat', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-heading: 'Poppins', sans-serif;
    --border-radius: 12px;
    --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    --box-shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.2);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
    --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
    --gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    line-height: 1.7;
    color: var(--text-dark);
    background-color: var(--white);
    font-size: 16px;
    font-weight: 400;
}

/* Typography Enhancements */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.02em;
    color: var(--text-dark);
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.8rem; }
h3 { font-size: 2.2rem; }
h4 { font-size: 1.8rem; }
h5 { font-size: 1.4rem; }
h6 { font-size: 1.2rem; }

p {
    font-size: 1.1rem;
    line-height: 1.7;
    color: var(--text-muted);
    margin-bottom: 1.5rem;
}

.lead {
    font-size: 1.3rem;
    font-weight: 400;
    line-height: 1.6;
    color: var(--text-dark);
}

/* Responsive Typography */
@media (max-width: 768px) {
    h1 { font-size: 2.5rem; }
    h2 { font-size: 2rem; }
    h3 { font-size: 1.8rem; }
    .hero-title { font-size: 2.8rem; }
    .section-title { font-size: 2.2rem; }
}

/* Header Styles - Professional IT Style */
.navbar {
    background-color: var(--white) !important;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 1rem 0;
    transition: var(--transition);
}

.navbar-brand {
    font-family: var(--font-heading);
    font-weight: 700;
    font-size: 1.8rem;
    color: var(--primary-color) !important;
    letter-spacing: -0.02em;
}

.navbar-nav .nav-link {
    font-family: var(--font-family);
    font-weight: 500;
    margin: 0 15px;
    color: var(--text-dark) !important;
    transition: var(--transition);
    position: relative;
    padding: 0.5rem 0 !important;
}

.navbar-nav .nav-link::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-accent);
    transition: width 0.3s ease;
}

.navbar-nav .nav-link:hover::after,
.navbar-nav .nav-link.active::after {
    width: 100%;
}

.navbar-nav .nav-link:hover {
    color: var(--accent-color) !important;
}

.navbar-toggler {
    border: none;
    padding: 0.25rem 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Hero Section */
.hero-section {
    background: var(--gradient-dark);
    color: var(--text-light);
    padding: 120px 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    opacity: 0.3;
}

.hero-section::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(6, 182, 212, 0.1) 100%);
    pointer-events: none;
}

.hero-title {
    font-family: var(--font-heading);
    font-size: 3.8rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    line-height: 1.1;
    letter-spacing: -0.02em;
    position: relative;
    z-index: 2;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2.5rem;
    opacity: 0.9;
    line-height: 1.6;
    max-width: 600px;
    position: relative;
    z-index: 2;
}

/* Section Styles */
.section-padding {
    padding: 100px 0;
}

.section-title {
    font-family: var(--font-heading);
    font-size: 2.8rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: 1.5rem;
    color: var(--text-dark);
    line-height: 1.2;
    letter-spacing: -0.02em;
}

.section-subtitle {
    font-size: 1.2rem;
    text-align: center;
    margin-bottom: 4rem;
    color: var(--text-muted);
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.6;
}

/* Card Styles - Professional IT Style */
.custom-card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    height: 100%;
    background: var(--white);
    position: relative;
    overflow: hidden;
}

.custom-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: var(--gradient-accent);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.custom-card:hover::before {
    transform: scaleX(1);
}

.custom-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--box-shadow-lg);
}

.card-icon {
    font-size: 3.5rem;
    color: var(--accent-color);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.custom-card:hover .card-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

/* Background Utilities */
.bg-light-custom {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.bg-primary {
    background: var(--gradient-primary) !important;
}

/* Button Styles - Professional IT Style */
.btn-primary-custom {
    background: var(--gradient-accent);
    border: none;
    color: var(--white);
    padding: 15px 35px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 1.1rem;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.btn-primary-custom::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary-custom:hover::before {
    left: 100%;
}

.btn-primary-custom:hover {
    background: var(--gradient-primary);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow-lg);
}

.btn-outline-primary-custom {
    border: 2px solid var(--accent-color);
    color: var(--accent-color);
    background-color: transparent;
    padding: 15px 35px;
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition);
    font-size: 1.1rem;
    letter-spacing: 0.5px;
}

.btn-outline-primary-custom:hover {
    background: var(--gradient-accent);
    border-color: var(--accent-color);
    color: var(--white);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

/* Footer Styles */
.footer {
    background-color: var(--dark-color);
    color: var(--white);
    padding: 50px 0 20px;
}

.footer h5 {
    color: var(--white);
    margin-bottom: 20px;
}

.footer a {
    color: #adb5bd;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid #495057;
    padding-top: 20px;
    margin-top: 30px;
    text-align: center;
    color: #adb5bd;
}

/* Testimonial Styles */
.testimonial-card {
    background: var(--white);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 30px;
}

.testimonial-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    margin: 0 auto 20px;
    object-fit: cover;
}

.testimonial-stars {
    color: #ffc107;
    font-size: 1.2rem;
    margin-bottom: 15px;
}

/* Contact Form Styles */
.contact-form {
    background: var(--light-color);
    padding: 40px;
    border-radius: 10px;
}

.form-control {
    border-radius: 5px;
    border: 1px solid #dee2e6;
    padding: 12px 15px;
    margin-bottom: 20px;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Pricing Card Styles */
.pricing-card {
    border: 2px solid #e9ecef;
    border-radius: 15px;
    padding: 40px 30px;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card:hover {
    border-color: var(--primary-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.pricing-price {
    font-size: 3rem;
    font-weight: bold;
    color: var(--primary-color);
    margin: 20px 0;
}

.pricing-features {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.pricing-features li {
    padding: 10px 0;
    border-bottom: 1px solid #f8f9fa;
}

.pricing-features li:last-child {
    border-bottom: none;
}

/* Blog Card Styles */
.blog-card {
    border: none;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    height: 100%;
}

.blog-card:hover {
    transform: translateY(-5px);
}

.blog-card img {
    height: 200px;
    object-fit: cover;
}

.blog-meta {
    color: var(--secondary-color);
    font-size: 0.9rem;
    margin-bottom: 10px;
}

/* Portfolio Styles */
.portfolio-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    margin-bottom: 30px;
}

.portfolio-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.portfolio-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 123, 255, 0.9);
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.portfolio-item:hover .portfolio-overlay {
    opacity: 1;
}

.portfolio-item:hover img {
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.1rem;
    }
    
    .section-title {
        font-size: 2rem;
    }
    
    .pricing-card.featured {
        transform: none;
    }
    
    .section-padding {
        padding: 50px 0;
    }
}

/* Utility Classes */
.text-primary-custom {
    color: var(--primary-color) !important;
}

.bg-light-custom {
    background-color: #f8f9fa !important;
}

.mt-100 {
    margin-top: 100px;
}

.mb-100 {
    margin-bottom: 100px;
}
