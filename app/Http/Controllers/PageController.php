<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class PageController extends Controller
{
    /**
     * Display the home page.
     */
    public function home()
    {
        return view('pages.home');
    }

    /**
     * Display the about us page.
     */
    public function about()
    {
        return view('pages.about');
    }

    /**
     * Display the services page.
     */
    public function services()
    {
        return view('pages.services');
    }

    /**
     * Display the portfolio page.
     */
    public function portfolio()
    {
        return view('pages.portfolio');
    }

    /**
     * Display the pricing page.
     */
    public function pricing()
    {
        return view('pages.pricing');
    }

    /**
     * Display the blog page.
     */
    public function blog()
    {
        return view('pages.blog');
    }

    /**
     * Display individual blog post.
     */
    public function blogPost($slug)
    {
        // In a real application, you would fetch the post from database
        return view('pages.blog-post', compact('slug'));
    }

    /**
     * Display the testimonials page.
     */
    public function testimonials()
    {
        return view('pages.testimonials');
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('pages.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function contactSubmit(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string|max:2000',
        ]);

        // In a real application, you would:
        // 1. Save to database
        // 2. Send email notification
        // 3. Send auto-reply to user

        // For now, just return success response
        return response()->json([
            'success' => true,
            'message' => 'Thank you for your message. We will get back to you soon!'
        ]);
    }

    /**
     * Handle newsletter subscription.
     */
    public function newsletterSubscribe(Request $request)
    {
        $request->validate([
            'email' => 'required|email|max:255',
        ]);

        // In a real application, you would save to database
        // and integrate with email marketing service

        return response()->json([
            'success' => true,
            'message' => 'Thank you for subscribing to our newsletter!'
        ]);
    }

    /**
     * Display the privacy policy page.
     */
    public function privacy()
    {
        return view('pages.privacy');
    }

    /**
     * Display the terms of service page.
     */
    public function terms()
    {
        return view('pages.terms');
    }
}
